const API_V1 = "api/v1/";
const API_V2 = "api/v2/";
const API_V2_SHORT = "v2/";

export enum EApiVersion {
    V1 = "V1",
    V2 = "V2",
    V3 = "V3",
    V2_SHORT = "V2_SHORT",
}

export enum EAccessType {
    INTERNAL = "INTERNAL",
    PRIVATE = "PRIVATE",
    PUBLIC = "PUBLIC",
}

interface IEndpointOptions {
    apiVersion?: EApiVersion;
    accessType?: EAccessType;
    useLocalhostMs?: boolean;
    useApiGateway?: boolean;
}

const getAccessTypeUrl = (accessType?: EAccessType): string => {
    if (accessType === EAccessType.PRIVATE) {
        return `${process.env.PRIVATE_ENDPOINT_PREFIX}${process.env.SECURED_ENDPOINT_PREFIX}`;
    }

    if (accessType === EAccessType.PUBLIC) {
        return process.env.PUBLIC_ENDPOINT_PREFIX;
    }

    return "";
};

const getApiVersion = (apiVersion?: EApiVersion): string => {
    if (apiVersion === EApiVersion.V1) {
        return API_V1;
    }

    if (apiVersion === EApiVersion.V2) {
        return API_V2;
    }

    if (apiVersion === EApiVersion.V2_SHORT) {
        return API_V2_SHORT;
    }

    return "";
};

export const getGatewayEndpoint = (useLocalhostGateway?: boolean) => {
    const endpoint = useLocalhostGateway ? process.env.LOCALHOST_API_GATEWAY_URL : process.env.API_URL;
    const apiGatewayUrl = useLocalhostGateway
        ? process.env.LOCALHOST_API_GATEWAY_ENDPOINT_PREFIX
        : process.env.API_GATEWAY_ENDPOINT_PREFIX;

    return `${endpoint}${apiGatewayUrl}`;
};

export const getMicroserviceEndpoint = (microServiceName: string, options: IEndpointOptions) => {
    const { apiVersion, accessType, useLocalhostMs, useApiGateway } = options;

    const endpoint = useLocalhostMs ? process.env.LOCALHOST_API_URL : process.env.API_URL;
    const accessUrl = getAccessTypeUrl(accessType);
    const apiVersionUrl = getApiVersion(apiVersion);
    const apiGatewayUrl = useApiGateway
        ? useLocalhostMs
            ? process.env.LOCALHOST_API_GATEWAY_ENDPOINT_PREFIX
            : process.env.API_GATEWAY_ENDPOINT_PREFIX
        : "";

    return `${endpoint}${apiGatewayUrl}${microServiceName}${apiVersionUrl}${accessUrl}`;
};

//Este es para las rutas relacionadas con Order-status-facade
export const getMicroserviceEndpointCustom = (microServiceName: string, options: IEndpointOptions) => {
    const { apiVersion, accessType, useLocalhostMs, useApiGateway } = options;

    const endpoint = useLocalhostMs ? process.env.LOCALHOST_API_URL : process.env.API_URL;
    const accessUrl = getAccessTypeUrl(accessType);
    const apiVersionUrl = getApiVersion(apiVersion);
    const apiGatewayUrl = useApiGateway
        ? useLocalhostMs
            ? process.env.LOCALHOST_API_GATEWAY_ENDPOINT_PREFIX
            : process.env.API_GATEWAY_ENDPOINT_PREFIX
        : "";

    return `${endpoint}${apiGatewayUrl}${apiVersionUrl}${microServiceName}${accessUrl}`;
};


export const PrivateEndpointOptions: IEndpointOptions = {
    accessType: EAccessType.PRIVATE,
};

export const PrivateV1EndpointOptions: IEndpointOptions = {
    accessType: EAccessType.PRIVATE,
    apiVersion: EApiVersion.V1,
};

export const PrivateV2EndpointOptions: IEndpointOptions = {
    accessType: EAccessType.PRIVATE,
    apiVersion: EApiVersion.V2,
};

export const PrivateV3EndpointOptions: IEndpointOptions = {
    accessType: EAccessType.PRIVATE,
    apiVersion: EApiVersion.V3,
};

export const PublicEndpointOptions: IEndpointOptions = {
    accessType: EAccessType.PUBLIC,
};

export const InternalV1EndpointOptions: IEndpointOptions = {
    accessType: EAccessType.INTERNAL,
    apiVersion: EApiVersion.V1,
    useApiGateway: true,
};

//Este es para las rutas relacionadas con Order-status-facade
export const InternalV2EndpointOptionsCustom: IEndpointOptions = {
    accessType: EAccessType.INTERNAL,
    apiVersion: EApiVersion.V2_SHORT,
    useApiGateway: true,
};


export const InternalEndpointOptions: IEndpointOptions = {
    accessType: EAccessType.INTERNAL,
    useApiGateway: true,
};

export const PublicEndpointApiGatewayOptions: IEndpointOptions = {
    accessType: EAccessType.PUBLIC,
    useApiGateway: true,
    apiVersion: EApiVersion.V1,
};

export const WithouApiGatewayOptions: IEndpointOptions = {
    apiVersion: EApiVersion.V1,
    useApiGateway: false,
};

export const WithApiGateWayOptionsAndVersion: IEndpointOptions = {
    apiVersion: EApiVersion.V1,
    useLocalhostMs: true,
    useApiGateway: true,
};

export const OnlyWithApiGatewayOptions: IEndpointOptions = {
    useLocalhostMs: true,
    useApiGateway: true,
};
