import { getMicroserviceEndpointCustom, InternalV2EndpointOptionsCustom, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { IAssignmentCompleteRequestDto, TCreateOrderPayload } from "../interfaces/payloads";
import { IOrderStatusFacadeCreateOrderVisitResponse } from "../interfaces/responses/IWFEOrderFacadeCreateOrderResponse";
import { IOrderStatusFacadeAssigmentCompleteResponse } from "../interfaces/responses/IWFEOrderFacadeAssigmentCompleteResponse";

const OSF_ENDPOINT_TEST = "v2/notification-service/";
const OSF_ENDPOINT = "notification-service/";
const OSF_PRIVATE_ENDPOINT = getMicroserviceEndpointCustom(OSF_ENDPOINT, InternalV2EndpointOptionsCustom);//InternalV2EndpointOptionsCustom);
const OSF_PRIVATE_ENDPOINT_TEST = "http://127.0.0.1:13419/";


export const assigmentComplete = (
    orderID: string,
    payload: IAssignmentCompleteRequestDto,
    options: IFetchOptions = {}
) => {
    const url = new URL(`${OSF_PRIVATE_ENDPOINT}field-service/order/${orderID}`);

    return fetcher<IOrderStatusFacadeAssigmentCompleteResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};


export const createOrderVisit = (payload: TCreateOrderPayload, options: IFetchOptions = {}) => {
    const url = new URL(`${OSF_PRIVATE_ENDPOINT}field-service/order-visits`);

    return fetcher<IOrderStatusFacadeCreateOrderVisitResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};
