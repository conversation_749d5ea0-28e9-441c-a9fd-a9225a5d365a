import { useEffect, useState } from "react";
import { STORAGE_KEY_SELECTED_CHANNEL } from "@constants";
import { useFetchState } from "@hooks/useFetchState";
import {
    getAccountSubscription,
    getOffersAddons,
    getActiveServices,
    getUniqueService,
} from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { IAddedAddon, IPropsUseAddNewOffer } from "./IAddNewOffer";
import { IOfferAddOns, IAddon } from "@services/subscription/accountId/interface/IAddOns";
import { ICollectionActiveServices } from "@services/subscription/accountId/interface/IActiveServices";
import { createOrder } from "@modules/wfeOrderFacade/apis/v0";
import { EWFEOrderFacadeUseCase } from "@modules/wfeOrderFacade/constants";
import { IModalInfo } from "@features/acquisition/PostpayAcquisition/Postpay/common/DepositUpfrontStep/IDepositUpfront";
import { IAddOn, TCreateOrderPayload } from "@modules/wfeOrderFacade/interfaces/payloads/ICreateOrderPayload";
import { useSnackBar } from "@common";
import { getErrorToDisplay } from "itsf-ui-common";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { IAccountSubscription } from "@services/subscription/accountId/interface/ISubscriptionsForId";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";

export const useAddNewOffer = ({ subscriptions, setSuccessRisk, setAddNewAddOns }: IPropsUseAddNewOffer) => {
    const { startFetching, endFetching, endFetchingError } = useFetchState();
    const [value, setValue] = useState(0);
    const { t } = useTranslation(["customer"]);
    const [offers, setOffers] = useState<IOfferAddOns[]>([]);
    const [subscriptionsActives, setSubscriptionsActives] = useState<ICollectionActiveServices[]>([]);
    const [addedAddons, setAddedAddons] = useState<IAddedAddon[]>([]);
    const [totalAddons, setTotalAddons] = useState<number>(0);
    const [subscriptionAccount, setSubscriptionAccount] = useState<IAccountSubscription[]>([]);
    const { setSnackBarError, setSnackBarSuccess } = useSnackBar();
    const [orderIdForSchedule, setOrderIdForSchedule] = useState<string>("");

    const handleAddClick = (addon: IAddon, offer: IOfferAddOns, currentActiveQty: number) => {
        setAddedAddons((prev) => {
            const existingAddon = prev.find((item) => item.addon.code === addon.code);
            const alreadyAdded = existingAddon ? existingAddon.quantity : 0;

            const totalUsed = currentActiveQty + alreadyAdded;

            if (totalUsed >= addon.maxQuantity) {
                setSnackBarSuccess(t("customer:isLimitReached", { totalUsed, maxQuantity: addon.maxQuantity }));

                return prev;
            }

            if (existingAddon) {
                return prev.map((item) =>
                    item.addon.code === addon.code ? { ...item, quantity: item.quantity + 1 } : item
                );
            }

            return [...prev, { addon, quantity: 1, offer }];
        });
    };

    const handleRemoveClick = (addon: IAddon, offer: IOfferAddOns) => {
        setAddedAddons((prev) => {
            const existingAddon = prev.find((item) => item.addon.code === addon.code);

            if (!existingAddon) {
                return prev;
            }

            if (existingAddon.quantity > 1) {
                return prev.map((item) =>
                    item.addon.code === addon.code ? { ...item, offer, quantity: item.quantity - 1 } : item
                );
            }

            return prev.filter((item) => item.addon.code !== addon.code);
        });
    };

    const [modalInfo, setModalInfo] = useState<IModalInfo & { orderReferences?: string[] }>();

    const servicesActives = async (_serviceId: string) => {
        try {
            startFetching();
            const response = await getActiveServices(_serviceId, {});
            setSubscriptionsActives((prev) => [...prev, ...response.collection]);
            endFetching();
        } catch (error) {
            endFetchingError(error);
        }
    };

    const callWfeFacade = async ({
        scheduleParams,
        contactDetails,
        subscriptionsList,
        appointmentConfirmation
    }: {
        scheduleParams: ISlot | undefined;
        contactDetails: TContactDetails | undefined;
        correlationId?: string;
        subscriptionsList: any[];
        appointmentConfirmation?: boolean;
    }) => {
        const orderReferences: string[] = [];
        let lastOrderId: string | undefined = undefined;
        function getAppointmentInfo(start?: string, finish?: string) {
            if (!start || !finish) {
                return {};
            }
            const startDate = new Date(start);
            const finishDate = new Date(finish);
            const appointmentDate = startDate.toISOString().split("T")[0];
            const startHour = startDate.getUTCHours();
            const finishHour = finishDate.getUTCHours();
            const startPeriod = startHour >= 12 ? "PM" : "AM";
            const finishPeriod = finishHour >= 12 ? "PM" : "AM";
            const appointmentTimeSlot = startPeriod === "PM" && finishPeriod === "PM" ? "PM" : "AM";

            return {
                appointmentDate,
                appointmentTimeSlot,
            };
        }

        const appointmentInfo = getAppointmentInfo(scheduleParams?.start, scheduleParams?.finish);

        // Mapeo de offer.code a serviceGroup
        const offerCodeToServiceGroup: Record<string, string> = {
            TV_CHANNEL: "TV",
            OTT: "INTERNET",
            BB_ADDON: "INTERNET",
            MATERIAL: "INTERNET",
        };

        
        // Agrupar addons por serviceGroup
        const addonsByServiceGroup: Record<string, IAddedAddon[]> = {};
        addedAddons.forEach((item) => {
            const group = offerCodeToServiceGroup[item.offer.code];
            if (!group) {
                return;
            }
            if (!addonsByServiceGroup[group]) {
                addonsByServiceGroup[group] = [];
            }
            addonsByServiceGroup[group].push(item);
        });
        console.log(" ### useAddNewOffer.tsx subscriptionsList ### ",subscriptionsList);
        console.log(" ### useAddNewOffer.tsx offerCodeToServiceGroup ### ",offerCodeToServiceGroup);
        console.log(" ### useAddNewOffer.tsx addonsByServiceGroup ### ",addonsByServiceGroup);
        console.log(" ### useAddNewOffer.tsx addedAddons ### ",addedAddons);

        // Para cada suscripción, si tiene addons, crear orden
        let atLeastOneOrder = false;
        for (const subscription of subscriptionsList) {
            const { id: serviceId, serviceGroup } = subscription;
            const addonsForService = addonsByServiceGroup[serviceGroup];
            if (!addonsForService || !addonsForService.length) {
                continue;
            }
            atLeastOneOrder = true;
            let orderIdReference: string | undefined;
            try {
                const { reference } = await getUniqueService();
                orderIdReference = reference;
            } catch (error) {
                setSnackBarError("No se pudo obtener el orderIdReference.");
                continue;
            }
            const order: TCreateOrderPayload = {
                orderId: orderIdReference,
                requestorUsername: contactDetails?.emails[0].email,
                useCase: EWFEOrderFacadeUseCase.ADD_ADDONS,
                serviceId: serviceId?.toString() ?? "",
                appointmentConfirmation: appointmentConfirmation ?? false,
                addOn: addonsForService.map((item) => ({
                    catalogCode: item.addon.code,
                    quantity: item.quantity,
                })) as unknown as IAddOn[],
                ...appointmentInfo,
            };
            try {
                startFetching();
                const response = await createOrder(order, true);
                orderReferences.push(response.orderId);
                lastOrderId = response.orderId;
            } catch (error) {
                setSnackBarError(getErrorToDisplay(error));
            } finally {
                endFetching();
            }
        }
        if (!atLeastOneOrder) {
            setSnackBarError("No hay add-ons seleccionados para las suscripciones disponibles.");
        } else if (orderReferences.length) {
            setOrderIdForSchedule(orderReferences[0]); 
            setModalInfo({
                orderReferences,
                onClick: () => handleRedirectAddAddon(appointmentConfirmation ?? false),
                orderReference: orderReferences[0],
            });
        }
    };


    const callWfeFacadeCancelationAddon = async ({
        contactDetails,
        appointmentConfirmation,
        addonId,
        serviceId,
        setOrderIdForSchedule,
    }: {
        contactDetails: TContactDetails | undefined;
        appointmentConfirmation?: boolean;
        addonId: number;
        serviceId: number;
        setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>; 
    }) => {
        const orderReferences: string[] = [];
        let lastOrderId: string | undefined = undefined;
        
        
        
        // Para cada suscripción, si tiene addons, crear orden
        let atLeastOneOrder = false;
            
            const order: TCreateOrderPayload = {
                //orderId: orderIdReference,
                requestorUsername: contactDetails?.emails[0].email,
                useCase: EWFEOrderFacadeUseCase.REMOVE_ADDONS,
                serviceId: serviceId,
                appointmentConfirmation: appointmentConfirmation ?? false,
                addOn: [{
                    id: addonId,
                }] as unknown as IAddOn[],
            };
            try {
                startFetching();
                const response = await createOrder(order, true);
                orderReferences.push(response.orderId);
                lastOrderId = response.orderId;
                setOrderIdForSchedule(response.orderId);
            } catch (error) {
                setSnackBarError(getErrorToDisplay(error));
            } finally {
                endFetching();
            }
        
        /*if (!atLeastOneOrder) {
            setSnackBarError("No hay add-on seleccionado para su cancelación.");
        } else if (orderReferences.length) {
            setOrderIdForSchedule(orderReferences[0]); 
            setModalInfo({
                orderReferences,
                onClick: () => handleRedirectAddAddon(appointmentConfirmation ?? false),
                orderReference: orderReferences[0],
            });
        }*/
    };


    const handleRedirectAddAddon = (appointmentConfirmation?: boolean) => {
        setSuccessRisk(null);
        if(!appointmentConfirmation){
            setAddNewAddOns(false);
        }        
    };

    useEffect(() => {
        if (addedAddons.length) {
            const total = addedAddons.reduce((acc, curr) => {
                const addonPrice = curr.addon.prices.reduce((priceAcc, price) => priceAcc + price.price, 0);

                return acc + addonPrice * curr.quantity;
            }, 0);
            setTotalAddons(total);
        }
    }, [addedAddons]);

    const selectedChannel = localStorage.getItem(STORAGE_KEY_SELECTED_CHANNEL);
    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const OfferAddOns = async ({ channelCode, serviceId }: { channelCode: string; serviceId: string }) => {
        try {
            startFetching();
            const response = await getOffersAddons(channelCode, serviceId);

            // eslint-disable-next-line no-console
            console.log("response.collection", response.collection);

            setOffers((prev) => {
                const existingCodes = new Set(prev.map((offer) => offer.code));

                const newOffers = response.collection.filter((offer) => !existingCodes.has(offer.code));

                return [...prev, ...newOffers];
            });

            endFetching();
        } catch (error) {
          //  endFetchingError(error);
        }
    };

    const serviceAccountSubscription = async (subscriptionId: string) => {
        try {
           
            startFetching();
            const response = await getAccountSubscription(subscriptionId);
            
            setSubscriptionAccount(response);
            endFetching();
        } catch (error) {
            endFetchingError(error);
        }
    };

    useEffect(() => {
        const fetchSubscriptions = async () => {
            if (subscriptions?.collection.length) {
                console.log("subscriptions.collection", subscriptions);
                for (const subscription of subscriptions.collection) {
                    await serviceAccountSubscription(subscription.id.toString());
                }
            }
        };

        fetchSubscriptions();
    }, [subscriptions]);

    /*useEffect(() => {
        if (subscriptions?.collection.length) {
            console.log("subscriptions.collection", subscriptions);
            // Filtrar las suscripciones que son de TV o INTERNET
            subscriptions.collection
                // .filter((itemFilter) => itemFilter.serviceGroup === "TV" || itemFilter.serviceGroup === "INTERNET")
                .forEach((subscription) => {
                    // eslint-disable-next-line no-console
                    console.log("¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿¿");
                    console.log(subscription.serviceGroup + " - " + subscription.id);

                    serviceAccountSubscription(subscription.id.toString());
                });
        }
    }, [subscriptions]);*/

    useEffect(() => {
        const channelCode = selectedChannel && JSON.parse(selectedChannel)?.channelGroup;
        if (subscriptionAccount.length) {
            
            subscriptionAccount.forEach(({ id }) => {
                // const subscription = subscriptions?.collection.find(
                //     (sub) =>
                //         sub.id.toString() === id.toString() ||
                //         sub.id.toString() ===
                //             subscriptionAccount.find((acc) => acc.id === id)?.subscriptionId?.toString()
                // );
                // const serviceGroup = subscription?.serviceGroup;

                OfferAddOns({
                    channelCode,
                    serviceId: id.toString(),
                });
                servicesActives(id.toString());
            });
        }
    }, [subscriptionAccount]);

    return {
        value,
        handleChange,
        offers,
        addedAddons,
        handleAddClick,
        handleRemoveClick,
        subscriptionsActives,
        totalAddons,
        callWfeFacade,
        modalInfo,
        handleRedirectAddAddon,
        setModalInfo,
        orderIdForSchedule,
        setOrderIdForSchedule,
        callWfeFacadeCancelationAddon,
    };
};
