import React, { useState, useMemo } from "react";
import {
    Box,
    Grid,
    Typography,
    Paper,
    Dialog,
    DialogActions,
    DialogContent,
} from "@mui/material";
import { OnOffSwitch } from "@common/OnOffSwitch/OnOffSwitch";
import { FormButton } from "@common/FormButton/FormButton";
import { ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { DialogTitleClose } from "@common/Dialog/DialogTitleClose/DialogTitleClose";
import { useStyle } from "../../style";
import CancelOrderConfirmationModal from "@common/styleComponents/OrderConfirmationModal/CancelOrderConfirmationModal";

interface IAddonsDeliveredStoreCartProps {
    addedAddons: ICollectionAddOns[];
    isVisible: boolean;
    onConfirm: (updatedAddons: ICollectionAddOns[]) => void;
    onCancel: () => void;
    onClose: () => void;
}

const AddonsDeliveredStoreCart: React.FC<IAddonsDeliveredStoreCartProps> = ({
    addedAddons,
    isVisible,
    onConfirm,
    onCancel,
    onClose,
}) => {
    const { t } = useTranslation(["common", "customer"]);
    const [showAskConfirmModal, setshowAskConfirmModal] = useState(false);
    const [showFinalConfirmModal, setshowFinalConfirmModal] = useState(false);
    const [addonStates, setAddonStates] = useState<Record<number, boolean>>({});
    const { classes } = useStyle();

    // Filtrar addons con itemGroupCode igual a 'MATERIAL'
    const materialAddons = useMemo(() => {
        if (!addedAddons) return [];
        return addedAddons.filter(
            (addon) => addon.itemGroupCode?.toUpperCase() === 'MATERIAL'
        );
    }, [addedAddons]);

    // Inicializar estados de los addons basado en recoveredEquipment
    React.useEffect(() => {
        const initialStates: Record<number, boolean> = {};
        materialAddons.forEach((addon) => {
            initialStates[addon.id] = addon.recoveredEquipment ?? false;
        });
        setAddonStates(initialStates);
    }, [materialAddons]);

    const handleToggleChange = (addonId: number, checked: boolean) => {
        setAddonStates(prev => ({
            ...prev,
            [addonId]: checked
        }));
    };

    const handleConfirmClick = () => {
        setshowAskConfirmModal(true);
    };

    const handleConfirmAction = () => {
        // Crear addons actualizados con los nuevos valores de recoveredEquipment
        const updatedAddons = materialAddons.map(addon => ({
            ...addon,
            recoveredEquipment: addonStates[addon.id] ?? false
        }));
        onConfirm(updatedAddons);
        setshowAskConfirmModal(false);
        setshowFinalConfirmModal(true);
        
    };

    const handleCancelModal = () => {
        setshowAskConfirmModal(false);
    };

    const handleCancelAction = () => {
        // Resetear estados a los valores originales
        const resetStates: Record<number, boolean> = {};
        materialAddons.forEach((addon) => {
            resetStates[addon.id] = addon.recoveredEquipment ?? false;
        });
        setAddonStates(resetStates);
        onCancel();
    };

    if (!isVisible) {
        return null;
    }

    return (
        <>
            <Box sx={{ pb: 1, pt: 2  }}>
                <Typography
                    className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}  sx={{ pb: 1 }}           
                >
                    {"Equipos Entregados en Tienda"}
                </Typography>
                        
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Seleccione cuáles equipos fueron recuperados y cuáles no se pudieron recuperar
                </Typography>

                {materialAddons.length === 0 ? (
                    <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                        No se encontraron addons de material
                    </Typography>
                ) : (
                    <Grid container spacing={2}>
                        {materialAddons.map((addon) => (
                            <Grid item xs={12} key={addon.id}>
                                <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Grid container alignItems="center" spacing={2}>
                                        <Grid item xs={3}>
                                            <Typography variant="body1" fontWeight="medium">
                                                {addon.description}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                ID: {addon.id}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Typography variant="body2" sx={{ color: "#565656" }}>
                                                Fecha de activación
                                            </Typography>
                                            <Typography variant="body2" sx={{ color: "text.secondary" }}>
                                                {addon.activatedAt ? dayjs(addon.activatedAt).format("DD/MM/YYYY") : "-"}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Typography variant="body2" sx={{ color: "#565656" }}>
                                                Serial
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {(() => {
                                                    const sn = addon.includedEquipmentsCodes?.[0]?.serialNumber;
                                                    const snText = sn == null ? "" : String(sn).trim();
                                                    return snText || "No disponible";
                                                })()}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                <OnOffSwitch
                                                    checked={addonStates[addon.id] ?? false}
                                                    handleChange={(checked) => handleToggleChange(addon.id, checked)}
                                                    inputProps={{ "aria-label": `Equipo recuperado ${addon.description}` }}
                                                />
                                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                                    {addonStates[addon.id] ? "Entregado" : "No entregado"}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    </Grid>
                                </Paper>
                            </Grid>
                        ))}
                    </Grid>
                )}

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
                    <FormButton
                        buttonText={t("common:confirm")}
                        color="primary"
                        variant="contained"
                        onClick={handleConfirmClick}
                        disabled={materialAddons.length === 0}
                    />
                </Box>
            </Box>






            {/* Modal para preguntar si desea confirmar los cambios */}
            <Dialog
                open={showAskConfirmModal}
                onClose={handleCancelModal}
                maxWidth="sm"
                fullWidth
            >
                
                <DialogTitleClose
                    handleClose={handleCancelModal}
                    title={t("common:confirmation")}
                />
                <DialogContent>
                    <Typography variant="body1">
                        ¿Está seguro de que desea confirmar los cambios en el estado de entrega de los equipos?
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <FormButton
                        buttonText={t("common:confirm")}
                        color="primary"
                        variant="contained"
                        onClick={handleConfirmAction}
                    />
                    <FormButton
                        buttonText={t("common:cancel")}
                        variant="outlined"
                        onClick={handleCancelModal}
                    />                    
                </DialogActions>
            </Dialog>

            <CancelOrderConfirmationModal
                isOpen={showFinalConfirmModal}
                message="Los cambios se han guardado correctamente."
                onClose={onClose}
            />
        </>
    );
};

export default AddonsDeliveredStoreCart;